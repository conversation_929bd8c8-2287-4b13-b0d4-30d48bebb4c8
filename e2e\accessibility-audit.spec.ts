import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

/**
 * Accessibility Audit Tests
 * Tests dashboard components for WCAG 2.2 compliance
 * Target: axe score ≥ 97
 * Includes responsive design testing at 320/768/1280px breakpoints
 */

test.describe('Accessibility Audit', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to main dashboard
    await page.goto('http://localhost:8080/dashboard');
    await expect(page.getByRole('main')).toBeVisible();
  });

  test('should pass axe accessibility audit on monitoring dashboard', async ({ page }) => {
    // Run axe accessibility scan
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
      .analyze();

    // Log results for debugging
    console.log('Accessibility violations:', accessibilityScanResults.violations.length);
    
    if (accessibilityScanResults.violations.length > 0) {
      console.log('Violations details:');
      accessibilityScanResults.violations.forEach((violation, index) => {
        console.log(`${index + 1}. ${violation.id}: ${violation.description}`);
        console.log(`   Impact: ${violation.impact}`);
        console.log(`   Help: ${violation.help}`);
        console.log(`   Nodes: ${violation.nodes.length}`);
      });
    }

    // Calculate accessibility score (100 - violations weighted by impact)
    const criticalViolations = accessibilityScanResults.violations.filter(v => v.impact === 'critical').length;
    const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
    const moderateViolations = accessibilityScanResults.violations.filter(v => v.impact === 'moderate').length;
    const minorViolations = accessibilityScanResults.violations.filter(v => v.impact === 'minor').length;

    // Weighted scoring: critical = -10, serious = -5, moderate = -2, minor = -1
    const score = Math.max(0, 100 - (criticalViolations * 10 + seriousViolations * 5 + moderateViolations * 2 + minorViolations * 1));
    
    console.log(`Accessibility Score: ${score}/100`);
    console.log(`Critical: ${criticalViolations}, Serious: ${seriousViolations}, Moderate: ${moderateViolations}, Minor: ${minorViolations}`);

    // Expect no critical or serious violations
    expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
    expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
    
    // Target score ≥ 97
    expect(score, 'Accessibility score should be ≥ 97').toBeGreaterThanOrEqual(97);
  });

  test('should pass axe audit on main dashboard', async ({ page }) => {
    await page.goto('http://localhost:8080/dashboard');
    await expect(page.getByRole('main')).toBeVisible();

    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
      .analyze();

    const criticalViolations = accessibilityScanResults.violations.filter(v => v.impact === 'critical').length;
    const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
    
    expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);
    expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);
  });

  test.describe('Responsive Design Tests', () => {
    const viewports = [
      { name: 'Mobile', width: 320, height: 568 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1280, height: 720 }
    ];

    viewports.forEach(({ name, width, height }) => {
      test(`should be accessible and functional at ${name} (${width}x${height})`, async ({ page }) => {
        // Set viewport size
        await page.setViewportSize({ width, height });
        
        // Navigate to monitoring dashboard
        await page.goto('http://localhost:8080/monitoring');
        await expect(page.getByRole('main')).toBeVisible();

        // Take screenshot for visual verification
        await page.screenshot({ 
          path: `e2e/screenshots/accessibility-${name.toLowerCase()}-${width}x${height}.png`,
          fullPage: true 
        });

        // Run accessibility audit at this viewport
        const accessibilityScanResults = await new AxeBuilder({ page })
          .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
          .analyze();

        const criticalViolations = accessibilityScanResults.violations.filter(v => v.impact === 'critical').length;
        const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;

        expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
        expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);

        // Test key interactive elements are accessible
        const refreshButtons = page.getByRole('button', { name: /refresh/i });
        const refreshButtonCount = await refreshButtons.count();
        
        for (let i = 0; i < refreshButtonCount; i++) {
          const button = refreshButtons.nth(i);
          await expect(button).toBeVisible();
          await expect(button).toBeEnabled();
          
          // Check focus is visible
          await button.focus();
          await expect(button).toBeFocused();
        }

        // Test navigation tabs are accessible
        const tabs = page.getByRole('tab');
        const tabCount = await tabs.count();
        
        for (let i = 0; i < tabCount; i++) {
          const tab = tabs.nth(i);
          await expect(tab).toBeVisible();
          
          // Check tab can be focused
          await tab.focus();
          await expect(tab).toBeFocused();
        }

        // Test form controls are accessible
        const selects = page.getByRole('combobox');
        const selectCount = await selects.count();
        
        for (let i = 0; i < selectCount; i++) {
          const select = selects.nth(i);
          if (await select.isVisible()) {
            await expect(select).toBeEnabled();
            
            // Check select can be focused
            await select.focus();
            await expect(select).toBeFocused();
          }
        }
      });
    });
  });

  test.describe('Focus Management Tests', () => {
    test('should have proper focus indicators on all interactive elements', async ({ page }) => {
      await page.goto('http://localhost:8080/monitoring');
      await expect(page.getByRole('main')).toBeVisible();

      // Test focus on buttons
      const buttons = page.getByRole('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 10); i++) { // Test first 10 buttons
        const button = buttons.nth(i);
        if (await button.isVisible() && await button.isEnabled()) {
          await button.focus();
          await expect(button).toBeFocused();
          
          // Check for enhanced focus class
          const hasEnhancedFocus = await button.evaluate(el => 
            el.classList.contains('focus-enhanced') || 
            el.classList.contains('focus-visible') ||
            window.getComputedStyle(el, ':focus-visible').outline !== 'none'
          );
          
          expect(hasEnhancedFocus, `Button ${i} should have visible focus indicator`).toBe(true);
        }
      }

      // Test focus on form controls
      const inputs = page.getByRole('textbox');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        if (await input.isVisible()) {
          await input.focus();
          await expect(input).toBeFocused();
        }
      }

      // Test focus on tabs
      const tabs = page.getByRole('tab');
      const tabCount = await tabs.count();
      
      for (let i = 0; i < Math.min(tabCount, 5); i++) { // Test first 5 tabs
        const tab = tabs.nth(i);
        if (await tab.isVisible()) {
          await tab.focus();
          await expect(tab).toBeFocused();
          
          // Check for enhanced focus class
          const hasEnhancedFocus = await tab.evaluate(el => 
            el.classList.contains('focus-enhanced') || 
            window.getComputedStyle(el, ':focus-visible').outline !== 'none'
          );
          
          expect(hasEnhancedFocus, `Tab ${i} should have visible focus indicator`).toBe(true);
        }
      }
    });

    test('should maintain focus visibility in modal contexts', async ({ page }) => {
      await page.goto('http://localhost:8080/monitoring');
      
      // Click RLS badge to open modal
      const rlsBadge = page.getByTestId('rls-policy-badge');
      if (await rlsBadge.isVisible()) {
        await rlsBadge.click();
        
        // Wait for modal to open
        await expect(page.getByRole('dialog')).toBeVisible();
        
        // Test focus within modal
        const modalButtons = page.getByRole('dialog').getByRole('button');
        const modalButtonCount = await modalButtons.count();
        
        for (let i = 0; i < modalButtonCount; i++) {
          const button = modalButtons.nth(i);
          if (await button.isVisible() && await button.isEnabled()) {
            await button.focus();
            await expect(button).toBeFocused();
            
            // Check z-index to ensure focus is not obscured
            const zIndex = await button.evaluate(el => 
              window.getComputedStyle(el).zIndex
            );
            expect(parseInt(zIndex) || 0, 'Modal button should have high z-index').toBeGreaterThanOrEqual(10);
          }
        }
        
        // Close modal
        await page.keyboard.press('Escape');
        await expect(page.getByRole('dialog')).not.toBeVisible();
      }
    });
  });

  test.describe('Color Contrast Tests', () => {
    test('should have sufficient color contrast for all text elements', async ({ page }) => {
      await page.goto('http://localhost:8080/monitoring');
      await expect(page.getByRole('main')).toBeVisible();

      // Run axe audit specifically for color contrast
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2aa'])
        .withRules(['color-contrast'])
        .analyze();

      const contrastViolations = accessibilityScanResults.violations.filter(v => v.id === 'color-contrast');
      
      if (contrastViolations.length > 0) {
        console.log('Color contrast violations:');
        contrastViolations.forEach(violation => {
          console.log(`- ${violation.description}`);
          violation.nodes.forEach(node => {
            console.log(`  Element: ${node.html}`);
            console.log(`  Target: ${node.target}`);
          });
        });
      }

      expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);
    });
  });
});
