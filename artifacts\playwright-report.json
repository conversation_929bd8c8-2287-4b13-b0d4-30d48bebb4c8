{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "artifacts/playwright-report.json"}], ["junit", {"outputFile": "artifacts/playwright-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.0", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:8080", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "accessibility-audit.spec.ts", "file": "accessibility-audit.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Accessibility Audit", "file": "accessibility-audit.spec.ts", "line": 11, "column": 6, "specs": [{"title": "should pass axe accessibility audit on monitoring dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 44596, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "snippet": "\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8\u001b[22m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getB<PERSON><PERSON><PERSON>('main')\u001b[22m\n\n\n\u001b[0m \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 16 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 17 |\u001b[39m\n \u001b[90m 18 |\u001b[39m   test(\u001b[32m'should pass axe accessibility audit on monitoring dashboard'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:15:42\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:38:47.324Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-2903863c9a400f78a843", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 44950, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "snippet": "\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8\u001b[22m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('main')\u001b[22m\n\n\n\u001b[0m \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 16 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 17 |\u001b[39m\n \u001b[90m 18 |\u001b[39m   test(\u001b[32m'should pass axe accessibility audit on monitoring dashboard'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:15:42\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:38:47.430Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-b013dbc9792d11479ce6", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on monitoring dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-c4beaef0be683deedfc5", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 8, "parallelIndex": 0, "status": "skipped", "duration": 1, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:41:02.243Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "133fb274c06e7193ffc5-0f948fa3623f5bb0e8c5", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on monitoring dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-f00ad82a998b357cfffb", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-b614128e75adea91a8a2", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on monitoring dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-ef8c6a593273fd14253c", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-e89eef7ca22fb0378a2d", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on monitoring dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-98a7e127a25c01584b91", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-a1744cd3be04261ccfd1", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on monitoring dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-59c3aba2741f6f1f2024", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-0c7276d45176eb636fae", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}], "suites": [{"title": "Responsive Design Tests", "file": "accessibility-audit.spec.ts", "line": 72, "column": 8, "specs": [{"title": "should be accessible and functional at Mobile (320x568)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 44050, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('main')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByR<PERSON>('main')\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:15:42", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}, "snippet": "\u001b[0m \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 16 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 17 |\u001b[39m\n \u001b[90m 18 |\u001b[39m   test(\u001b[32m'should pass axe accessibility audit on monitoring dashboard'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('main')\u001b[22m\n\n\n\u001b[0m \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 16 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 17 |\u001b[39m\n \u001b[90m 18 |\u001b[39m   test(\u001b[32m'should pass axe accessibility audit on monitoring dashboard'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:15:42\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:38:47.369Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-6ff64041a4179b495e20", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "timedOut", "duration": 45261, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "snippet": "\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8\u001b[22m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('main')\u001b[22m\n\n\n\u001b[0m \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 16 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 17 |\u001b[39m\n \u001b[90m 18 |\u001b[39m   test(\u001b[32m'should pass axe accessibility audit on monitoring dashboard'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:15:42\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:38:47.366Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-1a3e166534be339a02f4", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 2, "status": "timedOut", "duration": 60808, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 95}, "message": "Error: page.evaluate: Test timeout of 30000ms exceeded.\n Please check out https://github.com/dequelabs/axe-core-npm/blob/develop/packages/playwright/error-handling.md\n\n\u001b[0m \u001b[90m 93 |\u001b[39m\n \u001b[90m 94 |\u001b[39m         \u001b[90m// Run accessibility audit at this viewport\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 95 |\u001b[39m         \u001b[36mconst\u001b[39m accessibilityScanResults \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mAxeBuilder\u001b[39m({ page })\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 96 |\u001b[39m           \u001b[33m.\u001b[39mwithTags([\u001b[32m'wcag2a'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'wcag2aa'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'wcag21aa'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'wcag22aa'\u001b[39m])\n \u001b[90m 97 |\u001b[39m           \u001b[33m.\u001b[39manalyze()\u001b[33m;\u001b[39m\n \u001b[90m 98 |\u001b[39m\u001b[0m\n\u001b[2m    at AxeBuilder.analyze (C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\node_modules\\@axe-core\\playwright\\dist\\index.mjs:218:13)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:95:42\u001b[22m"}, {"message": "\u001b[31mTearing down \"context\" exceeded the test timeout of 30000ms.\u001b[39m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:39:43.663Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-70e29fd4688ba8c6abbf", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-aa202c5b44b99a94c53f", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-e43673ab422129bc4e02", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-2e6718374c5409719943", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-12f93b8e4e9b39bf3e96", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-0ed426c583c4e254edff", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-b04b11248624e7d1770b", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-ebbcc92fc1005986139b", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-68cfe31063b3d02bc91f", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-378c49c48dbbe11bd1f0", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-30b41b8dbd8fad859861", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-ee8f952f48515738b273", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-58099b26a6dccd0ab994", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-4e0d889eabee0ce955a5", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-ff65dcd2b9eef6f7b268", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-234d5085f62b2c8ae61f", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}]}, {"title": "Focus Management Tests", "file": "accessibility-audit.spec.ts", "line": 150, "column": 8, "specs": [{"title": "should have proper focus indicators on all interactive elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 1, "status": "timedOut", "duration": 58781, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "snippet": "\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8\u001b[22m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('main')\u001b[22m\n\n\n\u001b[0m \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 16 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 17 |\u001b[39m\n \u001b[90m 18 |\u001b[39m   test(\u001b[32m'should pass axe accessibility audit on monitoring dashboard'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:15:42\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:39:56.410Z", "annotations": [], "attachments": [{"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-bde5361b245d12a953e8", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 3, "status": "failed", "duration": 44941, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('main')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByR<PERSON>('main')\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:15:42", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}, "snippet": "\u001b[0m \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 16 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 17 |\u001b[39m\n \u001b[90m 18 |\u001b[39m   test(\u001b[32m'should pass axe accessibility audit on monitoring dashboard'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: getByRole('main')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('main')\u001b[22m\n\n\n\u001b[0m \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 16 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 17 |\u001b[39m\n \u001b[90m 18 |\u001b[39m   test(\u001b[32m'should pass axe accessibility audit on monitoring dashboard'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:15:42\u001b[22m"}, {"message": "\u001b[31mTearing down \"context\" exceeded the test timeout of 30000ms.\u001b[39m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:39:56.065Z", "annotations": [], "attachments": [{"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-58c63-isibility-in-modal-contexts-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 15}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-3f3e330ed4e0f04e92df", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-682887ca6a0d078ca50b", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-6163f18125713d498b6d", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-73244f85a61b8414e137", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-24e5e3416c1a03456aaa", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-ccbfb2803ddb36326d41", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-5475c5d247282550932e", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-0341b9e86f73c35782fa", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-f8bfa5c34a10eaaacbef", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-b11a441b9c9fe15a4a78", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-1c358357a22137a878ea", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}]}, {"title": "Color Contrast Tests", "file": "accessibility-audit.spec.ts", "line": 245, "column": 8, "specs": [{"title": "should have sufficient color contrast for all text elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "timedOut", "duration": 42041, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "snippet": "\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to monitoring dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/monitoring'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:39:59.656Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-bb4b4134f9d5daeba0db", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-16aea9165625ad26a840", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-f999fe7c9dbff59febe0", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-3c456bad02c863a75619", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-e81fae6c3ec76829bff6", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [], "status": "skipped"}], "id": "133fb274c06e7193ffc5-c64dca5632c22fdba5d2", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-06-19T20:38:42.493Z", "duration": 139845.91, "expected": 0, "skipped": 40, "unexpected": 8, "flaky": 0}}