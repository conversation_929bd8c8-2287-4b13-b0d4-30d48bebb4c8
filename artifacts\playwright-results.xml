<testsuites id="" name="" tests="48" failures="8" skipped="40" errors="0" time="139.84591">
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:38:43.166Z" hostname="chromium" tests="8" failures="8" skipped="0" time="385.428" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on monitoring dashboard" classname="accessibility-audit.spec.ts" time="44.596">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on monitoring dashboard" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on monitoring dashboard 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      10 |
      11 | test.describe('Accessibility Audit', () => {
    > 12 |   test.beforeEach(async ({ page }) => {
         |        ^
      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
      15 |     await expect(page.getByRole('main')).toBeVisible();
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:12:8

    Error: expect(locator).toBeVisible()

    Locator: getByRole('main')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for getByRole('main')


      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
    > 15 |     await expect(page.getByRole('main')).toBeVisible();
         |                                          ^
      16 |   });
      17 |
      18 |   test('should pass axe accessibility audit on monitoring dashboard', async ({ page }) => {
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:15:42

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-44048-dit-on-monitoring-dashboard-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="44.95">
<failure message="accessibility-audit.spec.ts:57:3 should pass axe audit on main dashboard" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:57:3 › Accessibility Audit › should pass axe audit on main dashboard 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      10 |
      11 | test.describe('Accessibility Audit', () => {
    > 12 |   test.beforeEach(async ({ page }) => {
         |        ^
      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
      15 |     await expect(page.getByRole('main')).toBeVisible();
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:12:8

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: getByRole('main')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for getByRole('main')


      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
    > 15 |     await expect(page.getByRole('main')).toBeVisible();
         |                                          ^
      16 |   });
      17 |
      18 |   test('should pass axe accessibility audit on monitoring dashboard', async ({ page }) => {
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:15:42

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="44.05">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Mobile (320x568)" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568) 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: getByRole('main')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for getByRole('main')


      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
    > 15 |     await expect(page.getByRole('main')).toBeVisible();
         |                                          ^
      16 |   });
      17 |
      18 |   test('should pass axe accessibility audit on monitoring dashboard', async ({ page }) => {
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:15:42

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="45.261">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Tablet (768x1024)" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024) 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      10 |
      11 | test.describe('Accessibility Audit', () => {
    > 12 |   test.beforeEach(async ({ page }) => {
         |        ^
      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
      15 |     await expect(page.getByRole('main')).toBeVisible();
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:12:8

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: getByRole('main')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for getByRole('main')


      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
    > 15 |     await expect(page.getByRole('main')).toBeVisible();
         |                                          ^
      16 |   });
      17 |
      18 |   test('should pass axe accessibility audit on monitoring dashboard', async ({ page }) => {
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:15:42

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="60.808">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Desktop (1280x720)" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720) 

    Test timeout of 30000ms exceeded.

    Error: page.evaluate: Test timeout of 30000ms exceeded.
     Please check out https://github.com/dequelabs/axe-core-npm/blob/develop/packages/playwright/error-handling.md

      93 |
      94 |         // Run accessibility audit at this viewport
    > 95 |         const accessibilityScanResults = await new AxeBuilder({ page })
         |                                          ^
      96 |           .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
      97 |           .analyze();
      98 |
        at AxeBuilder.analyze (C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\node_modules\@axe-core\playwright\dist\index.mjs:218:13)
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:95:42

    Tearing down "context" exceeded the test timeout of 30000ms.
]]>
</failure>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="58.781">
<failure message="accessibility-audit.spec.ts:151:5 should have proper focus indicators on all interactive elements" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:151:5 › Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      10 |
      11 | test.describe('Accessibility Audit', () => {
    > 12 |   test.beforeEach(async ({ page }) => {
         |        ^
      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
      15 |     await expect(page.getByRole('main')).toBeVisible();
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:12:8

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: getByRole('main')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for getByRole('main')


      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
    > 15 |     await expect(page.getByRole('main')).toBeVisible();
         |                                          ^
      16 |   });
      17 |
      18 |   test('should pass axe accessibility audit on monitoring dashboard', async ({ page }) => {
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:15:42

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="44.941">
<failure message="accessibility-audit.spec.ts:209:5 should maintain focus visibility in modal contexts" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:209:5 › Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts 

    Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

    Locator: getByRole('main')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for getByRole('main')


      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
    > 15 |     await expect(page.getByRole('main')).toBeVisible();
         |                                          ^
      16 |   });
      17 |
      18 |   test('should pass axe accessibility audit on monitoring dashboard', async ({ page }) => {
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:15:42

    Tearing down "context" exceeded the test timeout of 30000ms.

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-58c63-isibility-in-modal-contexts-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-58c63-isibility-in-modal-contexts-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="42.041">
<failure message="accessibility-audit.spec.ts:246:5 should have sufficient color contrast for all text elements" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:246:5 › Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      10 |
      11 | test.describe('Accessibility Audit', () => {
    > 12 |   test.beforeEach(async ({ page }) => {
         |        ^
      13 |     // Navigate to monitoring dashboard
      14 |     await page.goto('http://localhost:8080/monitoring');
      15 |     await expect(page.getByRole('main')).toBeVisible();
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:12:8

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:38:43.166Z" hostname="firefox" tests="8" failures="0" skipped="8" time="0.001" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on monitoring dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="0.001">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:38:43.166Z" hostname="webkit" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on monitoring dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:38:43.166Z" hostname="Mobile Chrome" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on monitoring dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:38:43.166Z" hostname="Mobile Safari" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on monitoring dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:38:43.166Z" hostname="Tablet" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on monitoring dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>