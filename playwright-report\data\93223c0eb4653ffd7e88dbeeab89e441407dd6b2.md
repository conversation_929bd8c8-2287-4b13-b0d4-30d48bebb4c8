# Page snapshot

```yaml
- region "Notifications (F8)":
  - list
- banner:
  - button "Back to Dashboard":
    - img
    - text: Back to Dashboard
  - img
  - heading "Monitoring Dashboard" [level=1]
  - text: Admin All Systems Operational
- main:
  - tablist:
    - tab "Overview" [selected]:
      - img
      - text: Overview
    - tab "Health":
      - img
      - text: Health
    - tab "Billing":
      - img
      - text: Billing
    - tab "Logs":
      - img
      - text: Logs
    - tab "Queue":
      - img
      - text: Queue
    - tab "Secrets":
      - img
      - text: Secrets
    - tab "Admin":
      - img
      - text: Admin
  - tabpanel "Overview":
    - heading "System Status" [level=3]:
      - img
      - text: System Status
    - text: Overall Health Healthy Uptime 99.9% Active Services 5/5
    - heading "Performance" [level=3]:
      - img
      - text: Performance
    - text: Avg Response 45ms Success Rate 99.8% Queue Length 3
    - heading "Usage & Costs" [level=3]:
      - img
      - text: Usage & Costs
    - text: This Month $12.45 API Calls 1,234 Budget Used 24%
    - heading "Recent Activity" [level=3]:
      - img
      - text: Recent Activity
    - text: Errors (24h) 2 Warnings (24h) 8 Last Deploy 2h ago
    - heading "Dashboard" [level=1]
    - paragraph: Monitor your code transformations, system health, and usage analytics
    - button "Code Editor":
      - img
      - text: Code Editor
    - tablist:
      - tab "Overview" [selected]:
        - img
        - text: Overview
      - tab "Repositories":
        - img
        - text: Repositories
      - tab "Analytics":
        - img
        - text: Analytics
      - tab "Admin":
        - img
        - text: Admin
      - tab "Workspace":
        - img
        - text: Workspace
      - tab "Alerts":
        - img
        - text: Alerts
    - tabpanel "Overview":
      - img
      - heading "Performance Metrics" [level=3]
      - img
      - button "Refresh performance metrics":
        - img
      - paragraph: Real-time system performance and health indicators
      - paragraph: System Status
      - paragraph: Critical
      - paragraph: Response Time
      - paragraph: 0ms
      - paragraph: Error Rate
      - paragraph: 0.00%
      - paragraph: Throughput
      - paragraph: 0.0 req/s
      - paragraph: Core Web Vitals
      - img
      - paragraph: LCP
      - paragraph: 42.8s
      - text: Poor
      - img
      - paragraph: CLS
      - paragraph: "0.009"
      - text: Good
      - img
      - paragraph: Memory Usage
      - text: 23.39 MB / 2.09 GB
      - progressbar
      - paragraph: "Last updated: 3:40:44 PM"
      - img
      - heading "Cost Tracking" [level=3]
      - button "Refresh cost tracking data":
        - img
      - paragraph: Real-time cost monitoring and budget tracking
      - alert:
        - img
        - text: Failed to load cost data
      - img
      - paragraph: Today's Spend
      - heading "$0.00" [level=6]
      - text: of $10.00 budget
      - img
      - paragraph: This Month
      - heading "$0.00" [level=6]
      - text: of $100.00 budget
      - img
      - paragraph: Avg per Transform
      - heading "$0.00" [level=6]
      - text: per transformation
      - img
      - paragraph: Projected Month
      - heading "$0.00" [level=6]
      - text: estimated total
      - paragraph: Budget Usage
      - paragraph: Daily Budget
      - text: 0.0%
      - progressbar
      - paragraph: Monthly Budget
      - text: 0.0%
      - progressbar
      - img
      - heading "System Health" [level=3]
      - img
      - text: Down
      - img
      - text: No RLS
      - button "Refresh system health data":
        - img
      - paragraph: Real-time monitoring of all system components and services
      - alert:
        - img
        - text: Failed to load system health data
      - paragraph: Overall Status
      - heading "Down" [level=6]
      - img
      - paragraph: System Uptime
      - heading "99.90%" [level=6]
      - img
      - paragraph: Services Online
      - heading "0 / 0" [level=6]
      - paragraph: Service Status
      - alert:
        - img
        - text: Some services are experiencing issues. Functionality may be limited.
      - paragraph: "Last checked: 3:40:44 PM"
      - img
      - heading "Quick Settings" [level=3]
      - paragraph: Quickly adjust model parameters and transformation settings
      - paragraph: Model Configuration
      - img
      - text: Planner Model
      - combobox:
        - option "GPT-4 Turbo ($0.01/1K tokens)" [selected]
        - option "GPT-4 ($0.03/1K tokens)"
        - option "GPT-3.5 Turbo ($0.002/1K tokens)"
        - option "Claude 3 Opus ($0.015/1K tokens)"
        - option "Claude 3 Sonnet ($0.003/1K tokens)"
      - img
      - text: Critic Model
      - combobox:
        - option "Claude 3 Sonnet ($0.003/1K tokens)" [selected]
        - option "Claude 3 Opus ($0.015/1K tokens)"
        - option "GPT-4 Turbo ($0.01/1K tokens)"
        - option "GPT-4 ($0.03/1K tokens)"
      - paragraph: Generation Parameters
      - img
      - text: Temperature 0.70
      - slider
      - text: Conservative Creative
      - img
      - text: Max Tokens
      - spinbutton: "4000"
      - paragraph: Loop Configuration
      - img
      - text: Max Iterations
      - spinbutton: "10"
      - img
      - text: Score Threshold
      - spinbutton: "0.95"
      - paragraph: Safety Limits
      - img
      - text: Cost Limit ($)
      - spinbutton: "3"
      - img
      - text: Timeout (seconds)
      - spinbutton: "180"
      - paragraph: Estimated Cost per Loop
      - text: $0.5200
      - paragraph: Based on current model selection and parameters
      - button "Reset" [disabled]:
        - img
        - text: Reset
      - button "Save Settings" [disabled]:
        - img
        - text: Save Settings
      - img
      - heading "Recent Activity" [level=3]
      - button "Refresh recent activity" [disabled]:
        - img
      - paragraph: Timeline of recent transformations, errors, and system events
      - img
      - textbox "Search activity..."
      - img
      - combobox:
        - option "All Events" [selected]
        - option "Transformations"
        - option "Errors"
        - option "System"
        - option "User Actions"
        - option "GitHub"
        - option "Billing"
      - img
      - paragraph: No recent activity
      - paragraph: Showing 0 of 0 events
      - paragraph: "Last updated: 3:40:43 PM"
```